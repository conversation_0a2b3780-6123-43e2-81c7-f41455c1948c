import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/game_state.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';
import '../services/token_service.dart';
import '../services/sound_service.dart';
import '../services/vibration_service.dart';
import 'app_providers.dart';

// Game State Notifier
class GameStateNotifier extends StateNotifier<GameState> {
  final GameConfig config;
  final TokenService _tokenService;
  final FirestoreService _firestoreService;
  final AuthService _authService;
  final SoundService _soundService;
  final VibrationService _vibrationService;
  
  Timer? _gameTimer;
  Timer? _countdownTimer;
  DateTime? _gameStartTime;

  GameStateNotifier({
    required this.config,
    required TokenService tokenService,
    required FirestoreService firestoreService,
    required AuthService authService,
    required SoundService soundService,
    required VibrationService vibrationService,
  }) : _tokenService = tokenService,
       _firestoreService = firestoreService,
       _authService = authService,
       _soundService = soundService,
       _vibrationService = vibrationService,
       super(GameState(
         lives: config.hasLives ? config.maxLives : 1,
         difficulty: config.difficulty,
       ));

  // Game lifecycle methods
  void startGame() {
    if (state.status != GameStatus.notStarted && state.status != GameStatus.gameOver) {
      return;
    }

    _gameStartTime = DateTime.now();
    state = GameState(
      status: GameStatus.playing,
      score: 0,
      level: 1,
      lives: config.hasLives ? config.maxLives : 1,
      difficulty: config.difficulty,
      gameTime: Duration.zero,
      isPaused: false,
    );

    _startGameTimer();
    _soundService.playGameStart();
    _vibrationService.onGameStart();
  }

  void pauseGame() {
    if (!state.canPause) return;

    state = state.copyWith(isPaused: true);
    _pauseGameTimer();
    _soundService.playButtonClick();
    _vibrationService.onButtonTap();
  }

  void resumeGame() {
    if (!state.canResume) return;

    state = state.copyWith(isPaused: false);
    _resumeGameTimer();
    _soundService.playButtonClick();
    _vibrationService.onButtonTap();
  }

  void endGame({String? reason}) {
    // Prevent duplicate endGame processing
    if (state.status == GameStatus.gameOver) return;

    state = state.copyWith(
      status: GameStatus.gameOver,
      isPaused: false,
      errorMessage: reason,
    );

    _stopGameTimer();
    _soundService.playGameOver();
    _vibrationService.onGameOver();

    // Submit score and award tokens
    _submitFinalScore();
  }

  void resetGame() {
    _stopGameTimer();
    state = GameState(
      status: GameStatus.notStarted,
      lives: config.hasLives ? config.maxLives : 1,
      difficulty: config.difficulty,
    );
  }

  // Score management
  void addScore(int points) {
    if (!state.isPlaying) return;

    final newScore = state.score + (points * config.scoreMultiplier);
    state = state.copyWith(score: newScore);
    
    _soundService.playScore();
    _vibrationService.onScore();
  }

  void setScore(int score) {
    if (!state.isPlaying) return;
    
    state = state.copyWith(score: score);
  }

  // Lives management
  void loseLife() {
    if (!config.hasLives || !state.isPlaying) return;

    final newLives = state.lives - 1;
    state = state.copyWith(lives: newLives);
    
    _vibrationService.onCollision();
    
    if (newLives <= 0) {
      endGame(reason: 'No lives remaining');
    }
  }

  void gainLife() {
    if (!config.hasLives || !state.isPlaying) return;

    state = state.copyWith(lives: state.lives + 1);
    _soundService.playPowerUp();
    _vibrationService.onPowerUp();
  }

  // Level management
  void nextLevel() {
    if (!config.hasLevels || !state.isPlaying) return;

    state = state.copyWith(level: state.level + 1);
    _soundService.playPowerUp();
    _vibrationService.onScore();
  }

  void setLevel(int level) {
    if (!config.hasLevels || !state.isPlaying) return;
    
    state = state.copyWith(level: level);
  }

  // Timer management
  void _startGameTimer() {
    if (!config.hasTimer) return;

    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.isPlaying) {
        final elapsed = DateTime.now().difference(_gameStartTime!);
        state = state.copyWith(gameTime: elapsed);

        // Check time limit
        if (config.timeLimit != null && elapsed >= config.timeLimit!) {
          endGame(reason: 'Time limit reached');
        }
      }
    });
  }

  void _pauseGameTimer() {
    _gameTimer?.cancel();
  }

  void _resumeGameTimer() {
    if (config.hasTimer && state.isPlaying && !state.isPaused) {
      _startGameTimer();
    }
  }

  void _stopGameTimer() {
    _gameTimer?.cancel();
    _countdownTimer?.cancel();
  }

  // Score submission
  Future<void> _submitFinalScore() async {
    try {
      final user = _authService.currentUser;
      if (user == null) return;

      // Submit to leaderboard
      await _firestoreService.submitScore(
        config.gameId,
        user.uid,
        user.displayName ?? 'Anonymous',
        state.score,
      );

      // Update user high score if needed
      final currentUser = await _firestoreService.getUser(user.uid);
      final currentHighScore = currentUser?.getHighScore(config.gameId) ?? 0;
      
      if (state.score > currentHighScore) {
        await _firestoreService.updateUserHighScore(
          user.uid,
          config.gameId,
          state.score,
        );
      }

      // Award tokens
      await _tokenService.awardGameTokens(config.gameId, state.score);
    } catch (e) {
      print('Error submitting score: $e');
    }
  }

  @override
  void dispose() {
    _stopGameTimer();
    super.dispose();
  }
}

// Provider for game state
final gameStateProvider = StateNotifierProvider.family<GameStateNotifier, GameState, GameConfig>(
  (ref, config) {
    final tokenService = ref.watch(tokenServiceProvider);
    final firestoreService = ref.watch(firestoreServiceProvider);
    final authService = ref.watch(authServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);

    return GameStateNotifier(
      config: config,
      tokenService: tokenService,
      firestoreService: firestoreService,
      authService: authService,
      soundService: soundService,
      vibrationService: vibrationService,
    );
  },
);

// Convenience providers for specific game state aspects
final gameScoreProvider = Provider.family<int, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).score;
});

final gameStatusProvider = Provider.family<GameStatus, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).status;
});

final gameIsPlayingProvider = Provider.family<bool, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).isPlaying;
});

final gameIsPausedProvider = Provider.family<bool, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).isPaused;
});

final gameLivesProvider = Provider.family<int, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).lives;
});

final gameLevelProvider = Provider.family<int, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).level;
});

final gameTimeProvider = Provider.family<Duration, GameConfig>((ref, config) {
  return ref.watch(gameStateProvider(config)).gameTime;
});
