import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/game_navigation_service.dart';
import '../../../core/providers/app_providers.dart';

class GameCard extends ConsumerWidget {
  final GameInfo gameInfo;
  final VoidCallback onTap;
  final bool isPinned;
  final VoidCallback? onPinToggle;

  const GameCard({
    super.key,
    required this.gameInfo,
    required this.onTap,
    this.isPinned = false,
    this.onPinToggle,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authService = ref.watch(authServiceProvider);
    final user = authService.currentUser;
    
    return Card(
      elevation: isPinned ? 6 : 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isPinned
          ? BorderSide(color: Colors.amber.shade400, width: 2)
          : BorderSide.none,
      ),
      child: Ink<PERSON>ell(
        onTap: onTap,
        onLongPress: onPinToggle,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                gameInfo.primaryColor.withValues(alpha: isPinned ? 0.15 : 0.1),
                gameInfo.secondaryColor.withValues(alpha: isPinned ? 0.1 : 0.05),
              ],
            ),
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: gameInfo.primaryColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            gameInfo.icon,
                            color: gameInfo.primaryColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                gameInfo.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: isPinned ? Colors.amber.shade700 : null,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                gameInfo.description,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        // Difficulty on the left, allow it to take only needed space
                        Flexible(
                          fit: FlexFit.loose,
                          child: _buildDifficultyIndicator(),
                        ),
                        const SizedBox(width: 8),
                        // Tags on the right; wrap to avoid overflow on small widths
                        Flexible(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Wrap(
                              spacing: 4,
                              runSpacing: 4,
                              alignment: WrapAlignment.end,
                              children: gameInfo.tags
                                  .take(2)
                                  .map((tag) => _buildTag(tag))
                                  .toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (isPinned)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.push_pin,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDifficultyIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < gameInfo.difficulty ? Icons.star : Icons.star_border,
          size: 16,
          color: index < gameInfo.difficulty 
            ? gameInfo.secondaryColor 
            : Colors.grey.shade400,
        );
      }),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: gameInfo.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: gameInfo.primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        tag,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: gameInfo.primaryColor,
        ),
      ),
    );
  }

  Future<int> _getUserHighScore(WidgetRef ref, String userId) async {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);
      final user = await firestoreService.getUser(userId);
      return user?.getHighScore(gameInfo.id) ?? 0;
    } catch (e) {
      return 0;
    }
  }
}
