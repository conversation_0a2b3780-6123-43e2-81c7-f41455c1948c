import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class FlapsterGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'flapster',
    gameName: 'Flapster',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const FlapsterGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _FlapsterGameContent();
  }
}

class Pipe {
  double x;
  final double gapY;
  final double gapHeight;
  bool scored;

  Pipe({
    required this.x,
    required this.gapY,
    this.gapHeight = 120,
    this.scored = false,
  });
}

class _FlapsterGameContent extends ConsumerStatefulWidget {
  const _FlapsterGameContent();

  @override
  ConsumerState<_FlapsterGameContent> createState() => _FlapsterGameContentState();
}

class _FlapsterGameContentState extends ConsumerState<_FlapsterGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _birdController;
  late AnimationController _flapController;
  
  // Bird physics
  double _birdY = 300;
  double _birdVelocityY = 0;
  final double _birdX = 80;
  final double _birdSize = 30;
  
  // Game mechanics
  final double _gravity = 800;
  final double _flapForce = -300;
  final double _pipeSpeed = 150;
  final double _pipeWidth = 60;
  
  // Pipes
  List<Pipe> _pipes = [];
  final Random _random = Random();

  // Local guard against multiple game over triggers
  bool _isGameOverTriggered = false;

  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;
  static const double _groundHeight = 80;

  @override
  void initState() {
    super.initState();
    
    _birdController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _flapController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _resetGame();
    _startGameLoop();

    // Listen for game state changes to reset when needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen(gameStateProvider(FlapsterGame.gameConfig), (previous, next) {
        if (previous?.status != next.status && next.status == GameStatus.notStarted) {
          _resetGame();
        }
      });
    });
  }

  @override
  void dispose() {
    _birdController.dispose();
    _flapController.dispose();
    super.dispose();
  }

  void _resetGame() {
    setState(() {
      _isGameOverTriggered = false;
      _birdY = 300;
      _birdVelocityY = 0;
      _pipes.clear();
      for (int i = 0; i < 3; i++) {
        _pipes.add(_createPipe(_gameWidth + i * 200));
      }
    });
  }

  Pipe _createPipe(double x) {
    final gapY = _random.nextDouble() * 300 + 150; // Gap between 150-450
    return Pipe(x: x, gapY: gapY);
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(FlapsterGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Update bird physics
        _birdVelocityY += _gravity / frameRate;
        _birdY += _birdVelocityY / frameRate;
        
        // Update pipes
        for (int i = 0; i < _pipes.length; i++) {
          _pipes[i].x -= _pipeSpeed / frameRate;
          
          // Check if bird passed through pipe
          if (!_pipes[i].scored && _pipes[i].x + _pipeWidth < _birdX) {
            _pipes[i].scored = true;
            final gameNotifier = ref.read(gameStateProvider(FlapsterGame.gameConfig).notifier);
            gameNotifier.addScore(1);
            ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
          }
        }
        
        // Remove off-screen pipes and add new ones
        _pipes.removeWhere((pipe) => pipe.x < -_pipeWidth);
        while (_pipes.length < 3) {
          final lastPipe = _pipes.isNotEmpty ? _pipes.last : null;
          final newX = lastPipe != null ? lastPipe.x + 200 : _gameWidth;
          _pipes.add(_createPipe(newX));
        }
        
        // Check collisions
        _checkCollisions();
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _checkCollisions() {
    // Check ground collision
    if (_birdY >= _gameHeight - _groundHeight - _birdSize / 2) {
      _gameOver();
      return;
    }
    
    // Check ceiling collision
    if (_birdY <= _birdSize / 2) {
      _gameOver();
      return;
    }
    
    // Check pipe collisions
    for (final pipe in _pipes) {
      if (_birdX + _birdSize / 2 > pipe.x && 
          _birdX - _birdSize / 2 < pipe.x + _pipeWidth) {
        
        // Check if bird is in the gap
        if (_birdY - _birdSize / 2 < pipe.gapY || 
            _birdY + _birdSize / 2 > pipe.gapY + pipe.gapHeight) {
          _gameOver();
          return;
        }
      }
    }
  }

  void _gameOver() {
    if (_isGameOverTriggered) return;
    _isGameOverTriggered = true;
    final gameNotifier = ref.read(gameStateProvider(FlapsterGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Collision detected');
  }

  void _onTap() {
    final gameState = ref.read(gameStateProvider(FlapsterGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      _birdVelocityY = _flapForce;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
    
    // Play animations
    _flapController.forward().then((_) => _flapController.reset());
    _birdController.forward().then((_) => _birdController.reset());
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(FlapsterGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFF98FB98)],
        ),
      ),
      child: GestureDetector(
        onTap: _onTap,
        child: Stack(
          children: [
            // Background elements
            _buildBackground(),
            
            // Pipes
            ..._pipes.map((pipe) => _buildPipe(pipe)),
            
            // Bird
            _buildBird(),
            
            // Ground
            _buildGround(),
            
            // Score display
            _buildScoreDisplay(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildPipe(Pipe pipe) {
    return Stack(
      children: [
        // Top pipe
        Positioned(
          left: pipe.x,
          top: 0,
          child: Container(
            width: _pipeWidth,
            height: pipe.gapY,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green[600]!, Colors.green[800]!],
              ),
              border: Border.all(color: Colors.green[900]!, width: 2),
            ),
          ),
        ),
        // Bottom pipe
        Positioned(
          left: pipe.x,
          top: pipe.gapY + pipe.gapHeight,
          child: Container(
            width: _pipeWidth,
            height: _gameHeight - pipe.gapY - pipe.gapHeight - _groundHeight,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green[600]!, Colors.green[800]!],
              ),
              border: Border.all(color: Colors.green[900]!, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBird() {
    return Positioned(
      left: _birdX - _birdSize / 2,
      top: _birdY - _birdSize / 2,
      child: RotationTransition(
        turns: Tween<double>(
          begin: 0,
          end: _birdVelocityY > 0 ? 0.1 : -0.1,
        ).animate(_birdController),
        child: ScaleTransition(
          scale: Tween<double>(begin: 1.0, end: 1.2).animate(_flapController),
          child: Container(
            width: _birdSize,
            height: _birdSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [Colors.yellow[400]!, Colors.orange[600]!],
                stops: const [0.3, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(2, 2),
                ),
              ],
            ),
            child: const Center(
              child: Text(
                '🐦',
                style: TextStyle(fontSize: 20),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGround() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: _groundHeight,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.brown[400]!, Colors.brown[600]!],
          ),
        ),
        child: CustomPaint(
          painter: _GroundPainter(),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Positioned(
      top: 50,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '${gameState.score}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 150,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap to flap and fly through the gaps!\nAvoid hitting pipes or the ground.\nScore points for each pipe passed.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw clouds
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    // Simple cloud shapes
    canvas.drawCircle(Offset(100, 80), 20, cloudPaint);
    canvas.drawCircle(Offset(120, 80), 25, cloudPaint);
    canvas.drawCircle(Offset(140, 80), 20, cloudPaint);
    
    canvas.drawCircle(Offset(300, 120), 15, cloudPaint);
    canvas.drawCircle(Offset(315, 120), 20, cloudPaint);
    canvas.drawCircle(Offset(330, 120), 15, cloudPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _GroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final grassPaint = Paint()
      ..color = Colors.green[400]!
      ..strokeWidth = 2;
    
    // Draw grass blades
    for (double x = 0; x < size.width; x += 10) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, 8),
        grassPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
