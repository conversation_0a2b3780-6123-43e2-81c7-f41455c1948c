import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class SwishShotGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'swish_shot',
    gameName: 'SwishShot',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const SwishShotGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _SwishShotGameContent();
  }
}

class _SwishShotGameContent extends ConsumerStatefulWidget {
  const _SwishShotGameContent();

  @override
  ConsumerState<_SwishShotGameContent> createState() => _SwishShotGameContentState();
}

class _SwishShotGameContentState extends ConsumerState<_SwishShotGameContent>
    with TickerProviderStateMixin {

  // Animation controllers
  late AnimationController _ballController;
  late AnimationController _hoopController;
  late AnimationController _scoreController;
  late AnimationController _trailController;

  // Ball physics
  Offset _ballPosition = const Offset(200, 500);
  Offset _ballVelocity = Offset.zero;
  bool _ballInMotion = false;
  List<Offset> _ballTrail = [];

  // Hoop state
  double _hoopX = 300;
  double _hoopDirection = 1;
  final double _hoopY = 250; // Lowered basket position
  final double _hoopWidth = 80;
  final double _hoopHeight = 20;
  final double _backboardWidth = 10;
  final double _backboardHeight = 100;

  // Game mechanics
  bool _scored = false;
  int _currentRound = 1;
  bool _wallBounceBeforeHoop = false;
  int _tokensEarned = 0;

  // Physics constants
  static const double _gravity = 280; // Lower gravity so arc is lighter
  static const double _ballSize = 54; // Slightly bigger ball
  static const double _borderThickness = 0; // No visual border, mechanics flush to screen edge
  static const double _wallBounceRetention = 0.9;

  // Game area dimensions (responsive)
  late double _gameWidth;
  late double _gameHeight;

  @override
  void initState() {
    super.initState();

    _ballController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _hoopController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _trailController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGameDimensions();
      _startNewRound();
    });
  }

  @override
  void dispose() {
    _ballController.dispose();
    _hoopController.dispose();
    _scoreController.dispose();
    _trailController.dispose();
    super.dispose();
  }

  void _initializeGameDimensions() {
    final size = MediaQuery.of(context).size;
    setState(() {
      _gameWidth = size.width;
      _gameHeight = size.height * 0.8; // Leave space for UI
      _ballPosition = Offset(_gameWidth / 2, _gameHeight - 100);
    });
  }

  void _startNewRound() {
    setState(() {
      _currentRound++;
      _wallBounceBeforeHoop = false;

      // Position hoop based on round
      if (_currentRound <= 4) {
        // Static hoop with random position
        _hoopX = _borderThickness + 50 +
                 math.Random().nextDouble() * (_gameWidth - _hoopWidth - _borderThickness * 2 - 100);
        _hoopController.stop();
      } else {
        // Moving hoop
        _startHoopMovement();
      }
    });
  }

  void _startHoopMovement() {
    if (_currentRound <= 4) return;

    _hoopController.repeat();
    _hoopController.addListener(() {
      setState(() {
        // Reduced speed: slower base speed and smaller increments
        double speed = _currentRound <= 9 ? 0.5 : (_currentRound - 9) * 0.2 + 0.5;
        _hoopX += _hoopDirection * speed;

        if (_hoopX <= _borderThickness + 20 ||
            _hoopX >= _gameWidth - _hoopWidth - _borderThickness - 20) {
          _hoopDirection *= -1;
        }
      });
    });
  }

  void _onPanEnd(DragEndDetails details) {
    final gameState = ref.read(gameStateProvider(SwishShotGame.gameConfig));
    if (!gameState.isPlaying || _ballInMotion) return;

    // Calculate velocity based on swipe gesture
    final velocity = details.velocity.pixelsPerSecond;

    // Only shoot if there's sufficient velocity (swipe, not tap)
    if (velocity.distance > 100) {
      _shootBall(velocity);
    }
  }

  void _shootBall(Offset velocity) {
    setState(() {
      // Fixed velocity - normalize to consistent speed
      final normalizedVelocity = velocity / velocity.distance;
      _ballVelocity = Offset(
        normalizedVelocity.dx * 300, // Fixed horizontal speed
        normalizedVelocity.dy * 300, // Fixed vertical speed
      );
      _ballInMotion = true;
      _scored = false;
      _wallBounceBeforeHoop = false;
      _ballTrail.clear();
    });

    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);

    _ballController.reset();
    _ballController.forward();
    _animateBall();
  }

  void _animateBall() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);

    void updateBall() {
      if (!_ballInMotion) return;

      setState(() {
        // Add current position to trail
        _ballTrail.add(_ballPosition);
        if (_ballTrail.length > 15) { // Longer trail
          _ballTrail.removeAt(0);
        }

        // Apply gravity
        _ballVelocity = Offset(
          _ballVelocity.dx,
          _ballVelocity.dy + _gravity / frameRate
        );

        // Calculate new position
        Offset newPosition = Offset(
          _ballPosition.dx + _ballVelocity.dx / frameRate,
          _ballPosition.dy + _ballVelocity.dy / frameRate,
        );

        // Check wall collisions
        newPosition = _checkWallCollisions(newPosition);

        // Update position
        _ballPosition = newPosition;

        // Check for hoop collision
        _checkHoopCollision();
      });

      if (_ballInMotion) {
        Future.delayed(frameDuration, updateBall);
      }
    }

    updateBall();
  }

  Offset _checkWallCollisions(Offset newPosition) {
    double newX = newPosition.dx;
    double newY = newPosition.dy;
    double newVelX = _ballVelocity.dx;
    double newVelY = _ballVelocity.dy;

    // Left wall collision (flush to screen edge)
    if (newX <= _ballSize / 2) {
      newX = _ballSize / 2;
      newVelX = -newVelX * _wallBounceRetention;
      _wallBounceBeforeHoop = true;
    }

    // Right wall collision
    if (newX >= _gameWidth - _borderThickness - _ballSize / 2) {
      newX = _gameWidth - _borderThickness - _ballSize / 2;
      newVelX = -newVelX * _wallBounceRetention;
      _wallBounceBeforeHoop = true;
    }

    // Top wall collision
    if (newY <= _borderThickness + _ballSize / 2) {
      newY = _borderThickness + _ballSize / 2;
      newVelY = -newVelY * _wallBounceRetention;
      _wallBounceBeforeHoop = true;
    }

    // Floor collision - END GAME instead of bouncing
    if (newY >= _gameHeight - _borderThickness - _ballSize / 2) {
      _handleGroundHit();
      return newPosition; // Return original position, game will end
    }

    _ballVelocity = Offset(newVelX, newVelY);
    return Offset(newX, newY);
  }

  void _checkHoopCollision() {
    final ballCenterX = _ballPosition.dx + _ballSize / 2;
    final ballCenterY = _ballPosition.dy + _ballSize / 2;

    // Check if ball hits the top of the hoop (rim)
    if (ballCenterX >= _hoopX &&
        ballCenterX <= _hoopX + _hoopWidth &&
        ballCenterY >= _hoopY - 5 &&
        ballCenterY <= _hoopY + 10 &&
        _ballVelocity.dy > 0 && // Ball must be falling
        !_scored) {

      _handleScore();
    }
  }

  void _handleScore() {
    setState(() {
      _scored = true;
    });

    // Calculate tokens: 1 for normal hoop, 2 if wall bounce before hoop
    int tokensForThisShot = _wallBounceBeforeHoop ? 2 : 1;
    _tokensEarned += tokensForThisShot;

    // Add score
    final gameNotifier = ref.read(gameStateProvider(SwishShotGame.gameConfig).notifier);
    gameNotifier.addScore(tokensForThisShot);

    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);

    // Play score animation
    _scoreController.forward().then((_) {
      _scoreController.reset();
    });

    _resetBall();
    _startNewRound();
  }

  void _handleGroundHit() {
    setState(() {
      _ballInMotion = false;
    });

    // Trigger feedback for ground hit
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);

    // End game immediately when ball hits ground
    _endGame();
  }



  void _endGame() async {
    final gameNotifier = ref.read(gameStateProvider(SwishShotGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Game Over - Ball hit the ground');

    // Update Firestore stats
    await _updateFirestoreStats();
  }

  Future<void> _updateFirestoreStats() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      final statsRef = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('gameStats')
          .doc('swishshotStats');

      await FirebaseFirestore.instance.runTransaction((transaction) async {
        final statsDoc = await transaction.get(statsRef);

        if (statsDoc.exists) {
          final data = statsDoc.data()!;
          final currentHighScore = data['highScore'] ?? 0;
          final currentTotalTokens = data['totalTokensEarned'] ?? 0;

          transaction.update(statsRef, {
            'highScore': math.max(currentHighScore, _tokensEarned),
            'totalTokensEarned': currentTotalTokens + _tokensEarned,
            'gamesPlayed': (data['gamesPlayed'] ?? 0) + 1,
            'lastPlayed': FieldValue.serverTimestamp(),
          });
        } else {
          transaction.set(statsRef, {
            'highScore': _tokensEarned,
            'totalTokensEarned': _tokensEarned,
            'gamesPlayed': 1,
            'firstPlayed': FieldValue.serverTimestamp(),
            'lastPlayed': FieldValue.serverTimestamp(),
          });
        }
      });

      // Award tokens through token service
      final tokenService = ref.read(tokenServiceProvider);
      await tokenService.addTokens(_tokensEarned, 'SwishShot game completion');

    } catch (e) {
      print('Error updating SwishShot stats: $e');
    }
  }

  void _resetBall() {
    setState(() {
      _ballPosition = Offset(_gameWidth / 2, _gameHeight - 100);
      _ballVelocity = Offset.zero;
      _ballInMotion = false;
      _ballTrail.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(SwishShotGame.gameConfig));

    return LayoutBuilder(
      builder: (context, constraints) {
        _gameWidth = constraints.maxWidth;
        _gameHeight = constraints.maxHeight;

        return Container(
          width: _gameWidth,
          height: _gameHeight,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFF1e3c72), Color(0xFF2a5298)],
            ),
          ),
          child: GestureDetector(
            onPanEnd: _onPanEnd,
            child: Stack(
              children: [
                // Ball trail
                _buildBallTrail(),

                // Hoop
                _buildHoop(),

                // Basketball
                _buildBall(),

                // Score feedback
                if (_scored) _buildScoreFeedback(),

                // Instructions
                if (!gameState.isPlaying) _buildInstructions(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameBorders() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _GameBorderPainter(_borderThickness),
      ),
    );
  }

  Widget _buildBallTrail() {
    if (_ballTrail.isEmpty) return const SizedBox.shrink();

    return Positioned.fill(
      child: CustomPaint(
        painter: _BallTrailPainter(_ballTrail),
      ),
    );
  }

  Widget _buildHoop() {
    return Positioned(
      left: _hoopX,
      top: _hoopY,
      child: Stack(
        children: [
          // Backboard
          Positioned(
            left: _hoopWidth - _backboardWidth,
            top: -_backboardHeight / 2,
            child: Container(
              width: _backboardWidth,
              height: _backboardHeight,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5DC), // Off-white
                border: Border.all(color: Colors.grey.shade400, width: 2),
              ),
            ),
          ),
          // Rim
          Container(
            width: _hoopWidth,
            height: _hoopHeight,
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5DC), // Off-white
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade400, width: 2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBall() {
    return Positioned(
      left: _ballPosition.dx - _ballSize / 2,
      top: _ballPosition.dy - _ballSize / 2,
      child: Container(
        width: _ballSize,
        height: _ballSize,
        child: const Center(
          child: Text(
            '🏀',
            style: TextStyle(fontSize: 36), // Larger emoji to match ball size
          ),
        ),
      ).animate(target: _ballInMotion ? 1 : 0)
       .scale(begin: const Offset(1, 1), end: const Offset(0.9, 1.1))
       .then()
       .scale(begin: const Offset(0.9, 1.1), end: const Offset(1, 1)),
    );
  }

  Widget _buildScoreFeedback() {
    return Positioned(
      left: _hoopX,
      top: _hoopY - 50,
      child: ScaleTransition(
        scale: _scoreController,
        child: const Text(
          'SWISH! 🎉',
          style: TextStyle(
            color: Colors.yellow,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: Offset(2, 2),
                blurRadius: 4,
                color: Colors.black54,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 40,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Score and tokens
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Score: ${gameState.score}',
                  style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  'Tokens: $_tokensEarned',
                  style: const TextStyle(color: Colors.yellow, fontSize: 14),
                ),
                Text(
                  'Round: $_currentRound',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          ),
          // Warning message
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(height: 4),
                Text(
                  'Don\'t hit\nthe ground!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 120,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white24, width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '🏀 SwishShot',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Swipe to shoot the basketball into the hoop!\n\n'
              '• Hit the rim to score\n'
              '• Wall bounce before hoop = 2x tokens\n'
              '• DON\'T let the ball hit the ground!\n'
              '• Hoop gets harder each round',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Tap to Start',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Custom painter for game borders
class _GameBorderPainter extends CustomPainter {
  final double borderThickness;

  _GameBorderPainter(this.borderThickness);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFF5F5DC) // Off-white
      ..style = PaintingStyle.fill;

    // Draw thick borders around all edges
    // Top border
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, borderThickness),
      paint,
    );

    // Bottom border
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - borderThickness, size.width, borderThickness),
      paint,
    );

    // Left border
    canvas.drawRect(
      Rect.fromLTWH(0, 0, borderThickness, size.height),
      paint,
    );

    // Right border
    canvas.drawRect(
      Rect.fromLTWH(size.width - borderThickness, 0, borderThickness, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom painter for ball trail effect
class _BallTrailPainter extends CustomPainter {
  final List<Offset> trail;

  _BallTrailPainter(this.trail);

  @override
  void paint(Canvas canvas, Size size) {
    if (trail.length < 2) return;

    for (int i = 0; i < trail.length - 1; i++) {
      final opacity = (i / trail.length) * 0.3; // Fade out effect
      final paint = Paint()
        ..color = Colors.orange.withValues(alpha: opacity)
        ..strokeWidth = 3
        ..style = PaintingStyle.stroke;

      canvas.drawCircle(trail[i], 8, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
