import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/store_service.dart';
import '../../core/providers/app_providers.dart';
import '../auth/widgets/account_upgrade_dialog.dart';

class StoreScreen extends ConsumerStatefulWidget {
  const StoreScreen({super.key});

  @override
  ConsumerState<StoreScreen> createState() => _StoreScreenState();
}

class _StoreScreenState extends ConsumerState<StoreScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _storeItems = [];
  List<Map<String, dynamic>> _boostItems = [];
  List<String> _ownedItems = [];
  int _userTokens = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadStoreData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStoreData() async {
    setState(() => _isLoading = true);
    
    try {
      final results = await Future.wait([
        StoreService.getStoreItems(),
        StoreService.getBoostItems(),
        StoreService.getUserOwnedItems(),
        StoreService.getUserTokens(),
      ]);

      setState(() {
        _storeItems = results[0] as List<Map<String, dynamic>>;
        _boostItems = results[1] as List<Map<String, dynamic>>;
        _ownedItems = results[2] as List<String>;
        _userTokens = results[3] as int;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading store: $e')),
        );
      }
    }
  }

  Future<void> _purchaseItem(String itemId, String itemType, int cost) async {
    if (_userTokens < cost) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Insufficient tokens!')),
      );
      return;
    }

    if (_ownedItems.contains(itemId)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('You already own this item!')),
      );
      return;
    }

    final result = await StoreService.purchaseItem(
      itemId: itemId,
      itemType: itemType,
      cost: cost,
    );

    if (result['success']) {
      setState(() {
        _userTokens = result['newTokenBalance'];
        _ownedItems.add(itemId);
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Successfully purchased $itemId!')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Purchase failed: ${result['error']}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final accountUpgradeService = ref.watch(accountUpgradeServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('TapVerse Store'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Skins & Flair', icon: Icon(Icons.palette)),
            Tab(text: 'Boosts', icon: Icon(Icons.flash_on)),
          ],
        ),
        actions: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.amber,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.monetization_on, color: Colors.black, size: 20),
                const SizedBox(width: 4),
                Text(
                  '$_userTokens',
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: accountUpgradeService.isAnonymousUser
          ? _buildAnonymousOverlay(context)
          : _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildItemsTab(_storeItems),
                    _buildBoostsTab(_boostItems),
                  ],
                ),
    );
  }

  Widget _buildItemsTab(List<Map<String, dynamic>> items) {
    if (items.isEmpty) {
      return const Center(
        child: Text('No items available'),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final isOwned = _ownedItems.contains(item['id']);
        final canAfford = _userTokens >= (item['cost'] ?? 0);

        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Item Image Placeholder
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: _getItemColor(item['rarity'] ?? 'common'),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getItemIcon(item['type'] ?? 'skin'),
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                
                // Item Name
                Text(
                  item['name'] ?? 'Unknown',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                
                // Item Description
                Text(
                  item['description'] ?? '',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 8),
                
                // Purchase Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: isOwned
                        ? null
                        : canAfford
                            ? () => _purchaseItem(
                                  item['id'],
                                  item['type'] ?? 'skin',
                                  item['cost'] ?? 0,
                                )
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isOwned
                          ? Colors.green
                          : canAfford
                              ? Colors.deepPurple
                              : Colors.grey,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: Text(
                      isOwned
                          ? 'OWNED'
                          : '${item['cost'] ?? 0} 🪙',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBoostsTab(List<Map<String, dynamic>> boosts) {
    if (boosts.isEmpty) {
      return const Center(
        child: Text('No boosts available'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: boosts.length,
      itemBuilder: (context, index) {
        final boost = boosts[index];
        final canAfford = _userTokens >= (boost['cost'] ?? 0);

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.orange,
              child: Icon(
                Icons.flash_on,
                color: Colors.white,
              ),
            ),
            title: Text(boost['name'] ?? 'Unknown Boost'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(boost['description'] ?? ''),
                if (boost['duration'] != null)
                  Text(
                    'Duration: ${boost['duration']} minutes',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
            trailing: ElevatedButton(
              onPressed: canAfford
                  ? () => _purchaseItem(
                        boost['id'],
                        'boost',
                        boost['cost'] ?? 0,
                      )
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: canAfford ? Colors.orange : Colors.grey,
                foregroundColor: Colors.white,
              ),
              child: Text('${boost['cost'] ?? 0} 🪙'),
            ),
          ),
        );
      },
    );
  }

  Color _getItemColor(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'common':
        return Colors.grey;
      case 'rare':
        return Colors.blue;
      case 'epic':
        return Colors.purple;
      case 'legendary':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getItemIcon(String type) {
    switch (type.toLowerCase()) {
      case 'skin':
        return Icons.palette;
      case 'flair':
        return Icons.star;
      case 'boost':
        return Icons.flash_on;
      default:
        return Icons.shopping_bag;
    }
  }

  Widget _buildAnonymousOverlay(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Icon(
                Icons.store_outlined,
                size: 64,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            Icon(
              Icons.lock_outline,
              size: 48,
              color: Colors.grey.shade600,
            ),
            const SizedBox(height: 16),
            Text(
              'Store Locked',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Upgrade your account to access the store and purchase items',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showUpgradeDialog(context),
              icon: const Icon(Icons.upgrade),
              label: const Text('Upgrade Account'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AccountUpgradeDialog(),
    );
  }
}
