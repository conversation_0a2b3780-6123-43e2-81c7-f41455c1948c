import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import 'game_card.dart';

class GamesPopupDialog extends ConsumerWidget {
  const GamesPopupDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final allGames = gameNavigation.allGames;

    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('All Games'),
          centerTitle: true,
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: allGames.isEmpty
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.games_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No games available',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              )
            : Padding(
                padding: const EdgeInsets.all(16),
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: allGames.length,
                  itemBuilder: (context, index) {
                    final game = allGames[index];
                    return GameCard(
                      gameInfo: game,
                      onTap: () {
                        Navigator.of(context).pop();
                        gameNavigation.navigateToGame(context, game.type);
                      },
                      onPinToggle: () => _showPinDialog(context, ref, game),
                    );
                  },
                ),
              ),
      ),
    );
  }

  void _showPinDialog(BuildContext context, WidgetRef ref, game) async {
    final pinnedGamesService = ref.read(pinnedGamesServiceProvider);
    final isPinned = await pinnedGamesService.isGamePinned(game.id);

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isPinned ? 'Unpin Game' : 'Pin Game'),
        content: Text(
          isPinned
              ? 'Remove "${game.name}" from pinned games?'
              : 'Pin "${game.name}" to the main menu for quick access?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              if (isPinned) {
                await pinnedGamesService.unpinGame(game.id);
              } else {
                await pinnedGamesService.pinGame(game.id);
              }

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isPinned
                          ? '${game.name} unpinned'
                          : '${game.name} pinned to main menu',
                    ),
                  ),
                );
              }
            },
            child: Text(isPinned ? 'Unpin' : 'Pin'),
          ),
        ],
      ),
    );
  }
}
