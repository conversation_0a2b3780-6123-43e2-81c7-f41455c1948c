import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/providers/app_providers.dart';
import '../widgets/settings_section.dart';
import '../../auth/widgets/account_upgrade_dialog.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  Future<void> _initializeSettings() async {
    final settingsService = ref.read(settingsServiceProvider);
    await settingsService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final settingsService = ref.watch(settingsServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);
    final authService = ref.watch(authServiceProvider);
    final accountUpgradeService = ref.watch(accountUpgradeServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Management
            SettingsSection(
              title: 'Account Management',
              icon: Icons.account_circle,
              children: [
                SwitchListTile(
                  title: const Text('Auto-Login'),
                  subtitle: const Text('Automatically sign in when app starts'),
                  value: settingsService.autoLoginEnabled,
                  onChanged: (value) async {
                    await settingsService.setAutoLoginEnabled(value);
                    setState(() {});
                  },
                ),
                if (accountUpgradeService.isAnonymousUser) ...[
                  ListTile(
                    leading: const Icon(Icons.upgrade),
                    title: const Text('Upgrade Account'),
                    subtitle: const Text('Create an account to save your progress'),
                    onTap: () => _showUpgradeDialog(context),
                  ),
                ] else ...[
                  ListTile(
                    leading: const Icon(Icons.logout, color: Colors.red),
                    title: const Text('Sign Out'),
                    subtitle: const Text('Return to login screen'),
                    onTap: () => _showSignOutDialog(context),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 24),

            // Audio Settings
            SettingsSection(
              title: 'Audio',
              icon: Icons.volume_up,
              children: [
                SwitchListTile(
                  title: const Text('Sound Effects'),
                  subtitle: const Text('Enable game sound effects'),
                  value: settingsService.soundEnabled,
                  onChanged: (value) async {
                    await settingsService.setSoundEnabled(value);
                    await soundService.setSoundEnabled(value);
                    setState(() {});
                  },
                ),
                SwitchListTile(
                  title: const Text('Background Music'),
                  subtitle: const Text('Enable background music'),
                  value: settingsService.musicEnabled,
                  onChanged: (value) async {
                    await settingsService.setMusicEnabled(value);
                    await soundService.setMusicEnabled(value);
                    setState(() {});
                  },
                ),
                ListTile(
                  title: const Text('Sound Volume'),
                  subtitle: Slider(
                    value: settingsService.soundVolume,
                    onChanged: settingsService.soundEnabled ? (value) async {
                      await settingsService.setSoundVolume(value);
                      await soundService.setSoundVolume(value);
                      setState(() {});
                    } : null,
                    divisions: 10,
                    label: '${(settingsService.soundVolume * 100).round()}%',
                  ),
                ),
                ListTile(
                  title: const Text('Music Volume'),
                  subtitle: Slider(
                    value: settingsService.musicVolume,
                    onChanged: settingsService.musicEnabled ? (value) async {
                      await settingsService.setMusicVolume(value);
                      await soundService.setMusicVolume(value);
                      setState(() {});
                    } : null,
                    divisions: 10,
                    label: '${(settingsService.musicVolume * 100).round()}%',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Haptic Settings
            SettingsSection(
              title: 'Haptics',
              icon: Icons.vibration,
              children: [
                SwitchListTile(
                  title: const Text('Vibration'),
                  subtitle: const Text('Enable haptic feedback'),
                  value: settingsService.vibrationEnabled,
                  onChanged: (value) async {
                    await settingsService.setVibrationEnabled(value);
                    await vibrationService.setVibrationEnabled(value);
                    setState(() {});
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Appearance Settings
            SettingsSection(
              title: 'Appearance',
              icon: Icons.palette,
              children: [
                SwitchListTile(
                  title: const Text('Dark Mode'),
                  subtitle: const Text('Use dark theme'),
                  value: settingsService.darkMode,
                  onChanged: (value) async {
                    await settingsService.setDarkMode(value);
                    setState(() {});
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Notifications Settings
            SettingsSection(
              title: 'Notifications',
              icon: Icons.notifications,
              children: [
                SwitchListTile(
                  title: const Text('Push Notifications'),
                  subtitle: const Text('Receive game updates and rewards'),
                  value: settingsService.notificationsEnabled,
                  onChanged: (value) async {
                    await settingsService.setNotificationsEnabled(value);
                    setState(() {});
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Data Management
            SettingsSection(
              title: 'Data Management',
              icon: Icons.storage,
              children: [
                ListTile(
                  leading: const Icon(Icons.refresh),
                  title: const Text('Reset Settings'),
                  subtitle: const Text('Reset all settings to default'),
                  onTap: () => _showResetSettingsDialog(context),
                ),
                ListTile(
                  leading: const Icon(Icons.delete_forever, color: Colors.red),
                  title: const Text('Reset All Data'),
                  subtitle: const Text('Delete all progress and start over'),
                  onTap: () => _showResetDataDialog(context),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // About Section
            SettingsSection(
              title: 'About',
              icon: Icons.info,
              children: [
                ListTile(
                  leading: const Icon(Icons.info_outline),
                  title: const Text('App Version'),
                  subtitle: const Text('1.0.0'),
                ),
                ListTile(
                  leading: const Icon(Icons.privacy_tip),
                  title: const Text('Privacy Policy'),
                  onTap: () => _showPrivacyPolicy(context),
                ),
                ListTile(
                  leading: const Icon(Icons.description),
                  title: const Text('Terms of Service'),
                  onTap: () => _showTermsOfService(context),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  void _showResetSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final settingsService = ref.read(settingsServiceProvider);
              await settingsService.resetToDefaults();
              setState(() {});
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Settings reset to defaults')),
                );
              }
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _showResetDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Data'),
        content: const Text(
          'This will permanently delete ALL your progress, including:\n\n'
          '• High scores\n'
          '• Tokens\n'
          '• Inventory items\n'
          '• Achievements\n'
          '• Settings\n\n'
          'This action cannot be undone. Are you absolutely sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => _showFinalConfirmationDialog(context),
            child: const Text('Delete Everything'),
          ),
        ],
      ),
    );
  }

  void _showFinalConfirmationDialog(BuildContext context) {
    Navigator.of(context).pop(); // Close previous dialog
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Final Confirmation'),
        content: const Text(
          'Type "DELETE" to confirm that you want to permanently delete all your data.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              Navigator.of(context).pop();
              
              final settingsService = ref.read(settingsServiceProvider);
              final success = await settingsService.resetUserData();
              
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('All data has been deleted')),
                );
                context.go('/login');
              } else if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Failed to delete data'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Confirm Delete'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'TapVerse Privacy Policy\n\n'
            'We collect minimal data necessary for game functionality:\n\n'
            '• Game scores and progress\n'
            '• User preferences and settings\n'
            '• Anonymous usage analytics\n\n'
            'We do not share personal data with third parties.\n\n'
            'Data is stored securely and can be deleted at any time.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms of Service'),
        content: const SingleChildScrollView(
          child: Text(
            'TapVerse Terms of Service\n\n'
            'By using this app, you agree to:\n\n'
            '• Use the app for entertainment purposes only\n'
            '• Not attempt to cheat or exploit the games\n'
            '• Respect other players in leaderboards\n'
            '• Not reverse engineer the app\n\n'
            'We reserve the right to modify these terms at any time.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AccountUpgradeDialog(),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out? You will be returned to the login screen.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final authService = ref.read(authServiceProvider);
              await authService.signOut();
              if (mounted) {
                context.go('/login');
              }
            },
            child: const Text('Sign Out', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
