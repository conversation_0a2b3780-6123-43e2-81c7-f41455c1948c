import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import '../widgets/profile_header.dart';
import '../widgets/stats_section.dart';
import '../widgets/high_scores_section.dart';
import '../widgets/inventory_preview.dart';
import '../widgets/achievements_section.dart';
import '../../auth/widgets/account_upgrade_dialog.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final userData = ref.watch(userDataProvider);
    final userStats = ref.watch(userStatisticsProvider);
    final userAchievementProgress = ref.watch(userAchievementProgressProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final accountUpgradeService = ref.watch(accountUpgradeServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => gameNavigation.navigateToSettings(context),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: authState.when(
        data: (user) {
          if (user == null) {
            return const Center(
              child: Text('Please log in to view your profile'),
            );
          }

          return userData.when(
            data: (userModel) {
              if (userModel == null) {
                return const Center(
                  child: Text('Profile data not found'),
                );
              }

              return RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(userDataProvider);
                  ref.invalidate(userStatisticsProvider);
                  ref.invalidate(userAchievementsProvider);
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Profile Header
                      ProfileHeader(
                        user: user,
                        userModel: userModel,
                        onSignOut: accountUpgradeService.isAnonymousUser
                            ? () => _showUpgradeDialog(context, ref)
                            : () => _showSignOutDialog(context, ref),
                        onEditProfile: () => _showEditProfileDialog(context, ref, userModel),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Stats Section
                      userStats.when(
                        data: (stats) => StatsSection(stats: stats),
                        loading: () => const StatsSection(stats: {}),
                        error: (_, __) => const StatsSection(stats: {}),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // High Scores Section
                      HighScoresSection(
                        highScores: userModel.highScores,
                        onViewLeaderboard: (gameId) => 
                            gameNavigation.navigateToLeaderboard(context, gameId: gameId),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Inventory Preview
                      InventoryPreview(
                        onViewInventory: () => gameNavigation.navigateToInventory(context),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Achievements Section
                      userAchievementProgress.when(
                        data: (achievementProgress) => AchievementsSection(achievementProgress: achievementProgress),
                        loading: () => const AchievementsSection(achievementProgress: {}),
                        error: (_, __) => const AchievementsSection(achievementProgress: {}),
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // Action Buttons
                      _buildActionButtons(context, ref, gameNavigation),
                      
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading profile',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.invalidate(userDataProvider);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Authentication error: $error'),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, WidgetRef ref, GameNavigationService gameNavigation) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => gameNavigation.navigateToStore(context),
            icon: const Icon(Icons.store),
            label: const Text('Visit Store'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => gameNavigation.navigateToLeaderboard(context),
            icon: const Icon(Icons.leaderboard),
            label: const Text('View Leaderboards'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => gameNavigation.navigateHome(context),
            icon: const Icon(Icons.home),
            label: const Text('Back to Games'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  void _showSignOutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final authService = ref.read(authServiceProvider);
              await authService.signOut();
              if (context.mounted) {
                context.go('/login');
              }
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context, WidgetRef ref, userModel) {
    final controller = TextEditingController(text: userModel.displayName);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Display Name',
            border: OutlineInputBorder(),
          ),
          maxLength: 20,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newName = controller.text.trim();
              if (newName.isNotEmpty && newName != userModel.displayName) {
                final userStatsService = ref.read(userStatsServiceProvider);
                final success = await userStatsService.updateDisplayName(
                  userModel.uid, 
                  newName,
                );
                
                if (success && context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Profile updated successfully')),
                  );
                  ref.invalidate(userDataProvider);
                } else if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Failed to update profile'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } else {
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => const AccountUpgradeDialog(),
    );
  }
}
