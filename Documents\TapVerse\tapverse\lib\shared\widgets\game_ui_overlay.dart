import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/game_state.dart';
import '../../core/providers/app_providers.dart';
import '../../core/providers/game_providers.dart';
import '../../core/providers/app_providers.dart';

class GameUIOverlay extends ConsumerWidget {
  final GameConfig config;
  final VoidCallback? onPause;
  final VoidCallback? onResume;
  final VoidCallback? onStart;
  final VoidCallback? onExit;

  const GameUIOverlay({
    super.key,
    required this.config,
    this.onPause,
    this.onResume,
    this.onStart,
    this.onExit,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gameState = ref.watch(gameStateProvider(config));
    final userTokens = ref.watch(userTokensProvider);

    return Stack(
      children: [
        // Top UI Bar
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: _buildTopBar(context, ref, gameState, userTokens),
        ),

        // Center overlay for game states
        if (gameState.status == GameStatus.notStarted)
          _buildStartOverlay(context),
        
        if (gameState.isPaused)
          _buildPauseOverlay(context),

        if (gameState.status == GameStatus.loading)
          _buildLoadingOverlay(context),

        // Bottom UI (if needed)
        if (config.hasLives)
          Positioned(
            bottom: 20,
            left: 20,
            child: _buildLivesDisplay(gameState),
          ),
      ],
    );
  }

  Widget _buildTopBar(BuildContext context, WidgetRef ref, GameState gameState, AsyncValue<int> userTokens) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
          ],
        ),
      ),
      child: Row(
        children: [
          // Exit button
          _buildIconButton(
            icon: Icons.arrow_back,
            onPressed: onExit,
            tooltip: 'Exit Game',
          ),
          
          const SizedBox(width: 16),
          
          // Score display
          Expanded(
            child: _buildScoreDisplay(gameState),
          ),
          
          const SizedBox(width: 16),
          
          // Timer (if enabled)
          if (config.hasTimer)
            _buildTimerDisplay(gameState),
          
          const SizedBox(width: 16),
          
          // Token display
          _buildTokenDisplay(userTokens),
          
          const SizedBox(width: 16),
          
          // Pause/Resume button
          if (gameState.status == GameStatus.playing)
            _buildIconButton(
              icon: gameState.isPaused ? Icons.play_arrow : Icons.pause,
              onPressed: gameState.isPaused ? onResume : onPause,
              tooltip: gameState.isPaused ? 'Resume' : 'Pause',
            ),
        ],
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
    Color? color,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: color ?? Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: Colors.amber,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Score: ${gameState.score}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (config.hasLevels) ...[
            const SizedBox(width: 16),
            Text(
              'Level: ${gameState.level}',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    ).animate()
     .fadeIn(duration: 300.ms)
     .slideY(begin: -0.5, end: 0, duration: 300.ms);
  }

  Widget _buildTimerDisplay(GameState gameState) {
    final minutes = gameState.gameTime.inMinutes;
    final seconds = gameState.gameTime.inSeconds % 60;
    final timeString = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

    Color timeColor = Colors.white;
    if (config.timeLimit != null) {
      final remaining = config.timeLimit! - gameState.gameTime;
      if (remaining.inSeconds <= 30) {
        timeColor = Colors.red;
      } else if (remaining.inSeconds <= 60) {
        timeColor = Colors.orange;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: timeColor.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            color: timeColor,
            size: 14,
          ),
          const SizedBox(width: 4),
          Text(
            timeString,
            style: TextStyle(
              color: timeColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenDisplay(AsyncValue<int> userTokens) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.amber.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.monetization_on,
            color: Colors.amber,
            size: 14,
          ),
          const SizedBox(width: 4),
          Text(
            userTokens.when(
              data: (tokens) => tokens.toString(),
              loading: () => '...',
              error: (_, __) => '0',
            ),
            style: const TextStyle(
              color: Colors.amber,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLivesDisplay(GameState gameState) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.red.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          for (int i = 0; i < config.maxLives; i++)
            Padding(
              padding: const EdgeInsets.only(right: 4),
              child: Icon(
                Icons.favorite,
                color: i < gameState.lives ? Colors.red : Colors.grey,
                size: 16,
              ),
            ),
        ],
      ),
    ).animate()
     .fadeIn(duration: 300.ms)
     .slideX(begin: -0.5, end: 0, duration: 300.ms);
  }

  Widget _buildStartOverlay(BuildContext context) {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Game Title
                Text(
                  config.gameName,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),

                // Game Instructions
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'How to Play',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _getGameInstructions(),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Leaderboard Preview
                _buildLeaderboardPreview(context),

                const SizedBox(height: 32),

                // Start Button
                ElevatedButton.icon(
                  onPressed: onStart,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Start Game'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    textStyle: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate()
     .fadeIn(duration: 500.ms)
     .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1), duration: 500.ms);
  }

  Widget _buildPauseOverlay(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.pause_circle_outline,
              color: Colors.white,
              size: 80,
            ),
            const SizedBox(height: 24),
            Text(
              'Game Paused',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: onResume,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Resume'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                textStyle: const TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
      ),
    ).animate()
     .fadeIn(duration: 300.ms);
  }

  Widget _buildLoadingOverlay(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 24),
            Text(
              'Loading...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    ).animate()
     .fadeIn(duration: 300.ms);
  }

  String _getGameInstructions() {
    // Basic instructions based on game type
    switch (config.gameId) {
      case 'swish_shot':
        return 'Tap and drag to aim, release to shoot the ball into the basket. Score as many points as possible!';
      case 'tappy_footy':
        return 'Tap to keep the ball in the air and avoid obstacles. Don\'t let it hit the ground!';
      case 'rebounder':
        return 'Move the paddle to keep the ball bouncing. Don\'t let it fall off the screen!';
      case 'spike_loop':
        return 'Tap to make the ball jump and avoid the spikes. Time your jumps carefully!';
      case 'flapster':
        return 'Tap to flap and navigate through the pipes. Don\'t hit the obstacles!';
      case 'smash_wall':
        return 'Move the paddle to bounce the ball and break all the bricks. Clear the wall to win!';
      default:
        return 'Follow the on-screen prompts and score as many points as possible. Good luck!';
    }
  }

  Widget _buildLeaderboardPreview(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final userStats = ref.watch(userStatisticsProvider);

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              const Icon(
                Icons.leaderboard,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                'Leaderboard',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              userStats.when(
                data: (stats) {
                  final gameStats = stats[config.gameId];
                  final userBest = gameStats?['highScore'] ?? 0;

                  return Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Your Best:',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                          Text(
                            '$userBest',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.amber,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Compete for the top spot!',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  );
                },
                loading: () => Text(
                  'Loading stats...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
                error: (_, __) => Text(
                  'Ready to set your first score?',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
