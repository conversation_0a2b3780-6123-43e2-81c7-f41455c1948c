import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'auth_service.dart';
import 'firestore_service.dart';

/// Service for upgrading anonymous accounts to full accounts
class AccountUpgradeService extends ChangeNotifier {
  final AuthService _authService;
  final FirestoreService _firestoreService;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  bool _showUpgradePopup = false;

  AccountUpgradeService({
    required AuthService authService,
    required FirestoreService firestoreService,
  }) : _authService = authService,
       _firestoreService = firestoreService;

  /// Get whether upgrade popup should be shown
  bool get showUpgradePopup => _showUpgradePopup && isAnonymousUser;

  /// Show upgrade popup for anonymous users
  void requestShowUpgradePopup() {
    if (isAnonymousUser) {
      _showUpgradePopup = true;
      notifyListeners();
    }
  }

  /// Hide upgrade popup
  void hideUpgradePopup() {
    _showUpgradePopup = false;
    notifyListeners();
  }

  /// Check if current user is anonymous
  bool get isAnonymousUser {
    final user = _authService.currentUser;
    return user?.isAnonymous ?? false;
  }

  /// Get anonymous user data before upgrade
  Future<Map<String, dynamic>?> getAnonymousUserData() async {
    final user = _authService.currentUser;
    if (user == null || !user.isAnonymous) return null;

    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        return userDoc.data();
      }
    } catch (e) {
      print('Error getting anonymous user data: $e');
    }
    
    return null;
  }

  /// Get anonymous user's game scores
  Future<List<Map<String, dynamic>>> getAnonymousUserScores() async {
    final user = _authService.currentUser;
    if (user == null || !user.isAnonymous) return [];

    try {
      final scores = <Map<String, dynamic>>[];
      
      // Get all leaderboard collections
      final leaderboards = await _firestore.collection('leaderboards').get();
      
      for (final leaderboardDoc in leaderboards.docs) {
        final gameId = leaderboardDoc.id;
        
        // Check if user has scores in this game
        final userScoreDoc = await _firestore
            .collection('leaderboards')
            .doc(gameId)
            .collection('scores')
            .doc(user.uid)
            .get();
            
        if (userScoreDoc.exists) {
          scores.add({
            'gameId': gameId,
            'data': userScoreDoc.data(),
          });
        }
      }
      
      return scores;
    } catch (e) {
      print('Error getting anonymous user scores: $e');
      return [];
    }
  }

  /// Upgrade anonymous account to email/password account
  Future<User?> upgradeToEmailPassword({
    required String email,
    required String password,
    required String displayName,
  }) async {
    final user = _authService.currentUser;
    if (user == null || !user.isAnonymous) {
      throw Exception('No anonymous user to upgrade');
    }

    try {
      // Store anonymous user data before upgrade
      final anonymousData = await getAnonymousUserData();
      final anonymousScores = await getAnonymousUserScores();
      final anonymousUid = user.uid;

      // Create email/password credential
      final credential = EmailAuthProvider.credential(
        email: email,
        password: password,
      );

      // Link the credential to the anonymous account
      final userCredential = await user.linkWithCredential(credential);
      final upgradedUser = userCredential.user;

      if (upgradedUser != null) {
        // Update display name
        await upgradedUser.updateDisplayName(displayName);

        // Update user document with new information
        await _updateUpgradedUserDocument(
          upgradedUser,
          displayName,
          anonymousData,
        );

        // Preserve all game scores (they're already linked to the same UID)
        // No need to migrate scores as the UID remains the same

        return upgradedUser;
      }
    } catch (e) {
      print('Error upgrading account: $e');
      rethrow;
    }

    return null;
  }

  /// Update user document after upgrade
  Future<void> _updateUpgradedUserDocument(
    User user,
    String displayName,
    Map<String, dynamic>? existingData,
  ) async {
    try {
      final userData = {
        'uid': user.uid,
        'email': user.email,
        'displayName': displayName,
        'isAnonymous': false,
        'createdAt': existingData?['createdAt'] ?? FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'accountUpgradedAt': FieldValue.serverTimestamp(),
        
        // Preserve existing data
        'tokens': existingData?['tokens'] ?? 0,
        'totalGamesPlayed': existingData?['totalGamesPlayed'] ?? 0,
        'totalScore': existingData?['totalScore'] ?? 0,
        'achievements': existingData?['achievements'] ?? [],
        'preferences': existingData?['preferences'] ?? {},
      };

      await _firestore
          .collection('users')
          .doc(user.uid)
          .set(userData, SetOptions(merge: true));
    } catch (e) {
      print('Error updating upgraded user document: $e');
      rethrow;
    }
  }

  /// Check if email is already in use
  Future<bool> isEmailAvailable(String email) async {
    try {
      final methods = await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);
      return methods.isEmpty;
    } catch (e) {
      print('Error checking email availability: $e');
      return false;
    }
  }

  /// Get upgrade benefits summary
  Map<String, dynamic> getUpgradeBenefits() {
    return {
      'dataSync': 'Your progress will be saved to the cloud',
      'multiDevice': 'Play on multiple devices with the same account',
      'leaderboards': 'Compete on global leaderboards',
      'socialFeatures': 'Add friends and share achievements',
      'dataBackup': 'Never lose your progress again',
      'exclusiveContent': 'Access to exclusive games and features',
    };
  }

  /// Get current anonymous user stats for display
  Future<Map<String, dynamic>> getAnonymousUserStats() async {
    final user = _authService.currentUser;
    if (user == null || !user.isAnonymous) {
      return {
        'tokens': 0,
        'gamesPlayed': 0,
        'totalScore': 0,
        'achievements': 0,
      };
    }

    try {
      final userData = await getAnonymousUserData();
      final scores = await getAnonymousUserScores();

      return {
        'tokens': userData?['tokens'] ?? 0,
        'gamesPlayed': userData?['totalGamesPlayed'] ?? 0,
        'totalScore': userData?['totalScore'] ?? 0,
        'achievements': (userData?['achievements'] as List?)?.length ?? 0,
        'gameScores': scores.length,
      };
    } catch (e) {
      print('Error getting anonymous user stats: $e');
      return {
        'tokens': 0,
        'gamesPlayed': 0,
        'totalScore': 0,
        'achievements': 0,
      };
    }
  }

  /// Create a new email/password account (for users who want to start fresh)
  Future<User?> createNewAccount({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      // Sign out current anonymous user
      await _authService.signOut();

      // Create new account
      final result = await _authService.createUserWithEmailAndPassword(
        email,
        password,
        displayName,
      );

      return result?.user;
    } catch (e) {
      print('Error creating new account: $e');
      rethrow;
    }
  }

  /// Validate upgrade form data
  Map<String, String?> validateUpgradeData({
    required String email,
    required String password,
    required String confirmPassword,
    required String displayName,
  }) {
    final errors = <String, String?>{};

    // Validate email
    if (email.trim().isEmpty) {
      errors['email'] = 'Email is required';
    } else if (!email.contains('@') || !email.contains('.')) {
      errors['email'] = 'Please enter a valid email address';
    }

    // Validate password
    if (password.isEmpty) {
      errors['password'] = 'Password is required';
    } else if (password.length < 6) {
      errors['password'] = 'Password must be at least 6 characters';
    }

    // Validate confirm password
    if (confirmPassword != password) {
      errors['confirmPassword'] = 'Passwords do not match';
    }

    // Validate display name
    if (displayName.trim().isEmpty) {
      errors['displayName'] = 'Display name is required';
    } else if (displayName.trim().length < 2) {
      errors['displayName'] = 'Display name must be at least 2 characters';
    }

    return errors;
  }
}
