import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class RebounderGame extends TapVerseBaseGame {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'rebounder',
    gameName: 'Rebounder',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const RebounderGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContentWithWalls(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _RebounderGameContent();
  }

  @override
  void startGame(WidgetRef ref) {
    // Game-specific startup logic will be handled by _RebounderGameContent
  }

  @override
  void resetGame(WidgetRef ref) {
    // Game-specific reset logic will be handled by _RebounderGameContent
  }

  @override
  void disposeGame(WidgetRef ref) {
    // Clean up any game-specific resources
  }
}

class _RebounderGameContent extends ConsumerStatefulWidget {
  const _RebounderGameContent();

  @override
  ConsumerState<_RebounderGameContent> createState() => _RebounderGameContentState();
}

class _RebounderGameContentState extends ConsumerState<_RebounderGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _ballController;
  late AnimationController _paddleController;
  
  // Ball physics
  Offset _ballPosition = const Offset(200, 300);
  Offset _ballVelocity = const Offset(150, 200);
  final double _ballSize = 20;
  
  // Paddle state
  double _paddleX = 150;
  final double _paddleY = 520;
  final double _paddleWidth = 100;
  final double _paddleHeight = 20;
  
  // Game mechanics
  double _currentSpeed = 1.0;
  final double _speedIncrement = 0.1;
  bool _ballHitPaddle = false;
  
  // Constants
  static const double _courtWidth = 400;
  static const double _courtHeight = 600;

  @override
  void initState() {
    super.initState();

    _ballController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _paddleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _resetGame();
    _startPhysicsLoop();

    // Listen for game state changes to reset when needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen(gameStateProvider(RebounderGame.gameConfig), (previous, next) {
        if (previous?.status != next.status && next.status == GameStatus.notStarted) {
          _resetGame();
        }
      });
    });
  }

  @override
  void dispose() {
    _ballController.dispose();
    _paddleController.dispose();
    super.dispose();
  }

  void _resetGame() {
    setState(() {
      _ballPosition = const Offset(200, 300);
      _ballVelocity = const Offset(150, 200);
      _paddleX = 150;
      _currentSpeed = 1.0;
      _ballHitPaddle = false;
    });
  }

  void _startPhysicsLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    
    void updatePhysics() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(RebounderGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, updatePhysics);
        return;
      }
      
      setState(() {
        // Update ball position
        _ballPosition = Offset(
          _ballPosition.dx + _ballVelocity.dx * _currentSpeed / frameRate,
          _ballPosition.dy + _ballVelocity.dy * _currentSpeed / frameRate,
        );
        
        // Check wall collisions
        _checkWallCollisions();
        
        // Check paddle collision
        _checkPaddleCollision();
        
        // Check if ball missed paddle (game over)
        if (_ballPosition.dy > _courtHeight + 50) {
          final gameNotifier = ref.read(gameStateProvider(RebounderGame.gameConfig).notifier);
          gameNotifier.endGame(reason: 'Ball missed paddle');
        }
      });
      
      Future.delayed(frameDuration, updatePhysics);
    }
    
    updatePhysics();
  }

  void _checkWallCollisions() {
    final collisionResult = TapVerseWallCollision.checkCircularCollision(
      position: _ballPosition,
      velocity: _ballVelocity,
      radius: _ballSize / 2,
      gameArea: Size(_courtWidth, _courtHeight),
    );

    if (collisionResult.collided) {
      _ballPosition = collisionResult.position;
      _ballVelocity = collisionResult.velocity;
      _triggerBounceEffect();
    }
  }

  void _checkPaddleCollision() {
    final ballLeft = _ballPosition.dx - _ballSize / 2;
    final ballRight = _ballPosition.dx + _ballSize / 2;
    final ballTop = _ballPosition.dy - _ballSize / 2;
    final ballBottom = _ballPosition.dy + _ballSize / 2;
    
    final paddleLeft = _paddleX;
    final paddleRight = _paddleX + _paddleWidth;
    final paddleTop = _paddleY;
    final paddleBottom = _paddleY + _paddleHeight;
    
    // Check if ball is colliding with paddle
    if (ballRight >= paddleLeft &&
        ballLeft <= paddleRight &&
        ballBottom >= paddleTop &&
        ballTop <= paddleBottom &&
        _ballVelocity.dy > 0) { // Ball must be moving downward
      
      // Calculate hit position on paddle (for angle calculation)
      final hitPosition = (_ballPosition.dx - _paddleX) / _paddleWidth;
      final angle = (hitPosition - 0.5) * pi / 3; // Max 60 degrees
      
      // Calculate new velocity based on hit position
      final speed = sqrt(_ballVelocity.dx * _ballVelocity.dx + _ballVelocity.dy * _ballVelocity.dy);
      _ballVelocity = Offset(
        sin(angle) * speed,
        -cos(angle) * speed, // Always bounce upward
      );
      
      // Ensure ball is above paddle
      _ballPosition = Offset(_ballPosition.dx, paddleTop - _ballSize / 2);
      
      // Increase score and speed
      final gameNotifier = ref.read(gameStateProvider(RebounderGame.gameConfig).notifier);
      gameNotifier.addScore(1);
      
      _currentSpeed += _speedIncrement;
      
      // Trigger feedback
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
      
      // Play animations
      _ballController.forward().then((_) => _ballController.reset());
      _paddleController.forward().then((_) => _paddleController.reset());
    }
  }

  void _triggerBounceEffect() {
    // Trigger feedback for wall bounce
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.targetHit);
    _ballController.forward().then((_) => _ballController.reset());
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final gameState = ref.read(gameStateProvider(RebounderGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      final constrainedPosition = TapVerseWallCollision.constrainRectangularObject(
        position: Offset(details.localPosition.dx - _paddleWidth / 2, _paddleY),
        objectSize: Size(_paddleWidth, _paddleHeight),
        gameArea: Size(_courtWidth, _courtHeight),
      );
      _paddleX = constrainedPosition.dx;
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(RebounderGame.gameConfig));
    
    return Container(
      width: _courtWidth,
      height: _courtHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF1a1a2e), Color(0xFF16213e)],
        ),
      ),
      child: GestureDetector(
        onPanUpdate: _onPanUpdate,
        child: Stack(
          children: [
            // Court background (walls are now provided by TapVerseBaseGame)
            Container(
              width: _courtWidth,
              height: _courtHeight,
              color: Colors.transparent,
            ),
            
            // Ball
            _buildBall(),
            
            // Paddle
            _buildPaddle(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }



  Widget _buildBall() {
    return Positioned(
      left: _ballPosition.dx - _ballSize / 2,
      top: _ballPosition.dy - _ballSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.3).animate(_ballController),
        child: Container(
          width: _ballSize,
          height: _ballSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.cyan[300]!, Colors.blue[700]!],
              stops: const [0.3, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withValues(alpha: 0.5),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaddle() {
    return Positioned(
      left: _paddleX,
      top: _paddleY,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.1).animate(_paddleController),
        child: Container(
          width: _paddleWidth,
          height: _paddleHeight,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange[400]!, Colors.red[600]!],
            ),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withValues(alpha: 0.5),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Speed',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${_currentSpeed.toStringAsFixed(1)}x',
                  style: const TextStyle(
                    color: Colors.cyan,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Drag the paddle to bounce the ball!\nBall gets faster with each hit.\nDon\'t let it fall!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}


