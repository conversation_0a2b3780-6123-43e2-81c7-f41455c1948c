import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import 'game_card.dart';

class GameGrid extends ConsumerStatefulWidget {
  const GameGrid({super.key});

  @override
  ConsumerState<GameGrid> createState() => _GameGridState();
}

class _GameGridState extends ConsumerState<GameGrid> {
  List<String> pinnedGameIds = [];

  @override
  void initState() {
    super.initState();
    _loadPinnedGames();
  }

  Future<void> _loadPinnedGames() async {
    final pinnedGamesService = ref.read(pinnedGamesServiceProvider);
    final pinned = await pinnedGamesService.getPinnedGames();
    if (mounted) {
      setState(() {
        pinnedGameIds = pinned;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);
    final pinnedGamesService = ref.watch(pinnedGamesServiceProvider);

    final allGames = gameNavigation.allGames;

    // Separate pinned and unpinned games
    final pinnedGames = <GameInfo>[];
    final unpinnedGames = <GameInfo>[];

    for (final game in allGames) {
      if (pinnedGameIds.contains(game.id)) {
        pinnedGames.add(game);
      } else {
        unpinnedGames.add(game);
      }
    }

    // Sort pinned games by their order in pinnedGameIds
    pinnedGames.sort((a, b) {
      final aIndex = pinnedGameIds.indexOf(a.id);
      final bIndex = pinnedGameIds.indexOf(b.id);
      return aIndex.compareTo(bIndex);
    });

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '🎮 Pinned Games',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (pinnedGames.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.push_pin,
                        size: 16,
                        color: Colors.amber.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${pinnedGames.length} pinned',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.amber.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Classic mini-games to earn tokens • Tap and hold to pin favorites',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: CustomScrollView(
              slivers: [
                // Pinned games section
                if (pinnedGames.isNotEmpty) ...[
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.push_pin,
                            size: 18,
                            color: Colors.amber.shade700,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Pinned Games',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.amber.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SliverGrid(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.9,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final game = pinnedGames[index];
                        return _buildGameCard(
                          game,
                          true,
                          index,
                          gameNavigation,
                          soundService,
                          vibrationService,
                          pinnedGamesService,
                        );
                      },
                      childCount: pinnedGames.length,
                    ),
                  ),
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 24),
                  ),
                ],

                // All games section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Text(
                      pinnedGames.isNotEmpty ? 'All Games' : 'All Games',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.9,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final game = unpinnedGames[index];
                      return _buildGameCard(
                        game,
                        false,
                        index + pinnedGames.length,
                        gameNavigation,
                        soundService,
                        vibrationService,
                        pinnedGamesService,
                      );
                    },
                    childCount: unpinnedGames.length,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameCard(
    GameInfo game,
    bool isPinned,
    int index,
    GameNavigationService gameNavigation,
    soundService,
    vibrationService,
    pinnedGamesService,
  ) {
    return GameCard(
      gameInfo: game,
      isPinned: isPinned,
      onTap: () async {
        await soundService.playButtonClick();
        await vibrationService.onButtonTap();
        if (mounted) {
          gameNavigation.navigateToGame(context, game.type);
        }
      },
      onPinToggle: () async {
        await pinnedGamesService.toggleGamePin(game.id);
        await _loadPinnedGames();
      },
    ).animate(delay: Duration(milliseconds: index * 50))
     .fadeIn(duration: 400.ms)
     .slideY(begin: 0.2, end: 0);
  }
}
