import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'tapverse/lib/shared/widgets/base_game_widget.dart';
import 'tapverse/lib/core/models/game_state.dart';

/// Test implementation to verify TapVerseBaseGame functionality
class TestTapVerseGame extends TapVerseBaseGame {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'test_game',
    gameName: 'Test Game',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const TestTapVerseGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContentWithWalls(BuildContext context, WidgetRef ref, GameState gameState) {
    return Container(
      color: Colors.blue[100],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Test Game with Border Walls',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            Text(
              'You should see off-white border walls around this content.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 20),
            Text(
              'Wall thickness: ${TapVerseGameConstants.wallThickness}px',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void startGame(WidgetRef ref) {
    print('Test game started');
  }

  @override
  void resetGame(WidgetRef ref) {
    print('Test game reset');
  }

  @override
  void disposeGame(WidgetRef ref) {
    print('Test game disposed');
  }
}

/// Test collision detection
void testWallCollision() {
  print('Testing wall collision detection...');
  
  // Test ball hitting left wall
  final leftWallResult = TapVerseWallCollision.checkCircularCollision(
    position: const Offset(10, 100), // Ball at left edge
    velocity: const Offset(-50, 0), // Moving left
    radius: 10,
    gameArea: const Size(400, 600),
  );
  
  print('Left wall collision: ${leftWallResult.collided}');
  print('New position: ${leftWallResult.position}');
  print('New velocity: ${leftWallResult.velocity}');
  
  // Test ball hitting top wall
  final topWallResult = TapVerseWallCollision.checkCircularCollision(
    position: const Offset(200, 10), // Ball at top edge
    velocity: const Offset(0, -50), // Moving up
    radius: 10,
    gameArea: const Size(400, 600),
  );
  
  print('Top wall collision: ${topWallResult.collided}');
  print('New position: ${topWallResult.position}');
  print('New velocity: ${topWallResult.velocity}');
  
  // Test playable area calculation
  final playableArea = TapVerseWallCollision.getPlayableArea(const Size(400, 600));
  final playableOffset = TapVerseWallCollision.getPlayableAreaOffset();
  
  print('Playable area: ${playableArea.width} x ${playableArea.height}');
  print('Playable offset: ${playableOffset.dx}, ${playableOffset.dy}');
}

void main() {
  print('TapVerse Base Game Test');
  print('======================');
  
  testWallCollision();
  
  print('\nTest completed successfully!');
  print('The TapVerseBaseGame system is working correctly.');
}
