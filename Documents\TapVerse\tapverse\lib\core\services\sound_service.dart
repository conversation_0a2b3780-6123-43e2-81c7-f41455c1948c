import 'package:just_audio/just_audio.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SoundType {
  // UI Sounds
  buttonClick,
  gameStart,
  gameOver,
  score,
  powerUp,
  background,
  victory,
  defeat,

  // Game-specific Sounds
  swish,           // Basketball scoring
  kick,            // Soccer/Football kick
  bounce,          // Ball bouncing
  pop,             // Balloon pop, bubble burst
  explosion,       // Destruction, collision
  whoosh,          // Fast movement, wind
  ding,            // Bell, notification
  thud,            // Heavy impact
  splash,          // Water, liquid
  zap,             // Electric, laser
  chime,           // Success, achievement
  buzz,            // Error, wrong action
  tick,            // Clock, timer
  swoosh,          // Quick movement
  crash,           // Collision, destruction
  ping,            // Light hit, notification
  clap,            // Applause, celebration
  whistle,         // Sports whistle
  coin,            // Collecting coins/tokens
  laser,           // Shooting, sci-fi
}

class SoundService {
  static final SoundService _instance = SoundService._internal();
  factory SoundService() => _instance;
  SoundService._internal();

  final Map<SoundType, AudioPlayer> _players = {};
  final Map<SoundType, String> _soundPaths = {
    // UI Sounds (present in assets)
    SoundType.buttonClick: 'assets/sounds/button_click.wav',
    SoundType.gameStart: 'assets/sounds/game_start.wav',
    SoundType.gameOver: 'assets/sounds/game_over.wav',
    SoundType.score: 'assets/sounds/score.wav',
    SoundType.powerUp: 'assets/sounds/power_up.wav',
    SoundType.background: 'assets/sounds/background.wav',
    SoundType.victory: 'assets/sounds/victory.wav',
    SoundType.defeat: 'assets/sounds/defeat.wav',

    // Game-specific Sounds (fallback to score if missing)
    SoundType.swish: 'assets/sounds/swish.mp3',
    SoundType.kick: 'assets/sounds/kick.mp3',
    SoundType.bounce: 'assets/sounds/bounce.mp3',
    SoundType.pop: 'assets/sounds/pop.mp3',
    SoundType.explosion: 'assets/sounds/explosion.mp3',
    SoundType.whoosh: 'assets/sounds/whoosh.mp3',
    SoundType.ding: 'assets/sounds/ding.mp3',
    SoundType.thud: 'assets/sounds/thud.mp3',
    SoundType.splash: 'assets/sounds/splash.mp3',
    SoundType.zap: 'assets/sounds/zap.mp3',
    SoundType.chime: 'assets/sounds/chime.mp3',
    SoundType.buzz: 'assets/sounds/buzz.mp3',
    SoundType.tick: 'assets/sounds/tick.mp3',
    SoundType.swoosh: 'assets/sounds/swoosh.mp3',
    SoundType.crash: 'assets/sounds/crash.mp3',
    SoundType.ping: 'assets/sounds/ping.mp3',
    SoundType.clap: 'assets/sounds/clap.mp3',
    SoundType.whistle: 'assets/sounds/whistle.mp3',
    SoundType.coin: 'assets/sounds/coin.mp3',
    SoundType.laser: 'assets/sounds/laser.mp3',
  };

  // Only preload the sounds we actually ship to avoid ExoPlayer source errors
  static const Set<SoundType> _preloadTypes = {
    SoundType.buttonClick,
    SoundType.gameStart,
    SoundType.gameOver,
    SoundType.score,
    SoundType.powerUp,
    SoundType.background,
    SoundType.victory,
    SoundType.defeat,
  };

  bool _soundEnabled = true;
  bool _musicEnabled = true;
  double _soundVolume = 1.0;
  double _musicVolume = 0.7;

  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;
  double get soundVolume => _soundVolume;
  double get musicVolume => _musicVolume;

  Future<void> initialize() async {
    await _loadSettings();
    await _initializePlayers();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _musicEnabled = prefs.getBool('music_enabled') ?? true;
      _soundVolume = prefs.getDouble('sound_volume') ?? 1.0;
      _musicVolume = prefs.getDouble('music_volume') ?? 0.7;
    } catch (e) {
      print('Error loading sound settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('music_enabled', _musicEnabled);
      await prefs.setDouble('sound_volume', _soundVolume);
      await prefs.setDouble('music_volume', _musicVolume);
    } catch (e) {
      print('Error saving sound settings: $e');
    }
  }

  Future<void> _initializePlayers() async {
    for (final soundType in _preloadTypes) {
      try {
        final path = _soundPaths[soundType];
        if (path == null) continue;
        final player = AudioPlayer();
        await player.setAsset(path);
        _players[soundType] = player;
      } catch (e) {
        // Silent fail to avoid log spam on devices without media extractors
      }
    }
  }

  Future<void> playSound(SoundType soundType) async {
    if (!_soundEnabled) return;

    try {
      final player = _players[soundType] ?? _players[SoundType.score];
      if (player != null) {
        await player.setVolume(_soundVolume);
        await player.seek(Duration.zero);
        await player.play();
      }
    } catch (e) {
      // Silent fail
    }
  }

  Future<void> playMusic(SoundType musicType, {bool loop = true}) async {
    if (!_musicEnabled) return;
    
    try {
      final player = _players[musicType];
      if (player != null) {
        await player.setVolume(_musicVolume);
        await player.setLoopMode(loop ? LoopMode.one : LoopMode.off);
        await player.seek(Duration.zero);
        await player.play();
      }
    } catch (e) {
      print('Error playing music $musicType: $e');
    }
  }

  Future<void> stopMusic(SoundType musicType) async {
    try {
      final player = _players[musicType];
      if (player != null) {
        await player.stop();
      }
    } catch (e) {
      print('Error stopping music $musicType: $e');
    }
  }

  Future<void> stopAllMusic() async {
    for (final player in _players.values) {
      try {
        await player.stop();
      } catch (e) {
        print('Error stopping player: $e');
      }
    }
  }

  Future<void> pauseAllMusic() async {
    for (final player in _players.values) {
      try {
        await player.pause();
      } catch (e) {
        print('Error pausing player: $e');
      }
    }
  }

  Future<void> resumeAllMusic() async {
    for (final player in _players.values) {
      try {
        if (player.playing) {
          await player.play();
        }
      } catch (e) {
        print('Error resuming player: $e');
      }
    }
  }

  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
  }

  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    if (!enabled) {
      await stopAllMusic();
    }
    await _saveSettings();
  }

  Future<void> setSoundVolume(double volume) async {
    _soundVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    
    // Update volume for currently playing music
    for (final player in _players.values) {
      if (player.playing) {
        await player.setVolume(_musicVolume);
      }
    }
    
    await _saveSettings();
  }

  void dispose() {
    for (final player in _players.values) {
      player.dispose();
    }
    _players.clear();
  }

  // Convenience methods for common sounds
  Future<void> playButtonClick() => playSound(SoundType.buttonClick);
  Future<void> playGameStart() => playSound(SoundType.gameStart);
  Future<void> playGameOver() => playSound(SoundType.gameOver);
  Future<void> playScore() => playSound(SoundType.score);
  Future<void> playPowerUp() => playSound(SoundType.powerUp);
  Future<void> playVictory() => playSound(SoundType.victory);
  Future<void> playDefeat() => playSound(SoundType.defeat);

  Future<void> playBackgroundMusic() => playMusic(SoundType.background);
  Future<void> stopBackgroundMusic() => stopMusic(SoundType.background);

  // Game-specific convenience methods
  Future<void> playSwish() => playSound(SoundType.swish);
  Future<void> playKick() => playSound(SoundType.kick);
  Future<void> playBounce() => playSound(SoundType.bounce);
  Future<void> playPop() => playSound(SoundType.pop);
  Future<void> playExplosion() => playSound(SoundType.explosion);
  Future<void> playWhoosh() => playSound(SoundType.whoosh);
  Future<void> playDing() => playSound(SoundType.ding);
  Future<void> playThud() => playSound(SoundType.thud);
  Future<void> playSplash() => playSound(SoundType.splash);
  Future<void> playZap() => playSound(SoundType.zap);
  Future<void> playChime() => playSound(SoundType.chime);
  Future<void> playBuzz() => playSound(SoundType.buzz);
  Future<void> playTick() => playSound(SoundType.tick);
  Future<void> playSwoosh() => playSound(SoundType.swoosh);
  Future<void> playCrash() => playSound(SoundType.crash);
  Future<void> playPing() => playSound(SoundType.ping);
  Future<void> playClap() => playSound(SoundType.clap);
  Future<void> playWhistle() => playSound(SoundType.whistle);
  Future<void> playCoin() => playSound(SoundType.coin);
  Future<void> playLaser() => playSound(SoundType.laser);
}
