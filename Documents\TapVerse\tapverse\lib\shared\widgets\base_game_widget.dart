import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/game_state.dart';
import '../../core/providers/game_providers.dart';
import '../../core/providers/app_providers.dart';
import '../../core/services/game_feedback_service.dart';
import '../dialogs/game_over_dialog.dart';
import 'game_ui_overlay.dart';

// Constants for consistent wall styling across all games
class TapVerseGameConstants {
  static const double wallThickness = 20.0;
  static const Color wallColor = Color(0xFFF5F5F5); // Off-white
  static const Color wallBorderColor = Color(0xFFE0E0E0); // Slightly darker border
  static const double wallBorderWidth = 1.0;
}

/// Abstract base class for all games in TapVerse
/// Provides common functionality like scoring, pause/resume, game over handling,
/// sound/vibration integration, and token rewards
abstract class BaseGameWidget extends ConsumerStatefulWidget {
  final GameConfig config;
  final VoidCallback? onGameComplete;
  final VoidCallback? onGameExit;

  const BaseGameWidget({
    super.key,
    required this.config,
    this.onGameComplete,
    this.onGameExit,
  });

  @override
  ConsumerState<BaseGameWidget> createState() => _BaseGameWidgetState();

  /// Override this method to build your game's content
  /// This is where you implement your specific game logic and UI
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState);

  /// Override this method to handle game-specific initialization
  /// Called when the game starts
  void onGameStart(WidgetRef ref) {}

  /// Override this method to handle game-specific pause logic
  /// Called when the game is paused
  void onGamePause(WidgetRef ref) {}

  /// Override this method to handle game-specific resume logic
  /// Called when the game is resumed
  void onGameResume(WidgetRef ref) {}

  /// Override this method to handle game-specific cleanup
  /// Called when the game ends or widget is disposed
  void onGameEnd(WidgetRef ref) {}

  /// Override this method to handle custom game updates
  /// Called every frame when the game is playing
  void onGameUpdate(WidgetRef ref, Duration deltaTime) {}

  /// Helper method to trigger game feedback
  /// Can be called from game implementations
  void triggerFeedback(WidgetRef ref, GameEvent event, {FeedbackConfig? customConfig}) {
    final feedbackService = ref.read(gameFeedbackServiceProvider);
    feedbackService.triggerFeedback(event, customConfig: customConfig);
  }

  /// Helper method to trigger combo feedback
  void triggerComboFeedback(WidgetRef ref, int comboCount) {
    final feedbackService = ref.read(gameFeedbackServiceProvider);
    feedbackService.triggerComboFeedback(comboCount);
  }

  /// Helper method to trigger countdown feedback
  void triggerCountdownFeedback(WidgetRef ref, int secondsRemaining) {
    final feedbackService = ref.read(gameFeedbackServiceProvider);
    feedbackService.triggerCountdownFeedback(secondsRemaining);
  }
}

class _BaseGameWidgetState extends ConsumerState<BaseGameWidget>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  DateTime? _lastFrameTime;
  bool _isInitialized = false;


  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // Initialize the game after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGame();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _fadeController.dispose();
    _scaleController.dispose();
    
    // Clean up game-specific resources
    widget.onGameEnd(ref);
    
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    final gameState = ref.read(gameStateProvider(widget.config));
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        if (gameState.isPlaying) {
          gameNotifier.pauseGame();
        }
        break;
      case AppLifecycleState.resumed:
        // Don't auto-resume, let user manually resume
        break;
      case AppLifecycleState.detached:
        widget.onGameEnd(ref);
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  void _initializeGame() {
    if (_isInitialized) return;
    
    _isInitialized = true;
    _fadeController.forward();
    
    // Initialize sound service
    ref.read(soundServiceProvider).initialize();
    ref.read(vibrationServiceProvider).initialize();
  }

  void _startGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.startGame();
    widget.onGameStart(ref);
    _startGameLoop();
  }

  void _pauseGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.pauseGame();
    widget.onGamePause(ref);
  }

  void _resumeGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.resumeGame();
    widget.onGameResume(ref);
    _startGameLoop();
  }

  void _endGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.endGame();
    widget.onGameEnd(ref);
  }

  void _startGameLoop() {
    _lastFrameTime = DateTime.now();
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(widget.config));
      if (!gameState.isPlaying) return;
      
      final now = DateTime.now();
      final deltaTime = _lastFrameTime != null 
          ? now.difference(_lastFrameTime!) 
          : Duration.zero;
      _lastFrameTime = now;
      
      // Call game-specific update
      widget.onGameUpdate(ref, deltaTime);
      
      // Schedule next frame
      WidgetsBinding.instance.addPostFrameCallback((_) => gameLoop());
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) => gameLoop());
  }

  void _showGameOverDialog(GameState gameState) async {
    final dialog = await GameOverDialog.create(
      ref: ref,
      config: widget.config,
      score: gameState.score,
      onPlayAgain: () {
        final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
        gameNotifier.resetGame();
        _startGame();
      },
      onHome: () {
        widget.onGameExit?.call();
      },
      onLeaderboard: () {
        // TODO: Navigate to leaderboard
        widget.onGameComplete?.call();
      },
    );

    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => dialog,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(widget.config));
    
    // Show game over dialog when game ends
    ref.listen(gameStateProvider(widget.config), (previous, current) {
      if (previous?.status != GameStatus.gameOver && 
          current.status == GameStatus.gameOver) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showGameOverDialog(current);
        });
      }
    });

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeController,
          child: Stack(
            children: [
              // Game content
              Positioned.fill(
                child: widget.buildGameContent(context, ref, gameState),
              ),
              
              // Game UI overlay (score, pause button, etc.)
              Positioned.fill(
                child: GameUIOverlay(
                  config: widget.config,
                  onPause: _pauseGame,
                  onResume: _resumeGame,
                  onStart: _startGame,
                  onExit: widget.onGameExit,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Abstract base class for TapVerse mini-games with enhanced lifecycle management
/// and consistent border wall functionality
abstract class TapVerseBaseGame extends BaseGameWidget {
  const TapVerseBaseGame({
    super.key,
    required super.config,
    super.onGameComplete,
    super.onGameExit,
  });

  @override
  ConsumerState<TapVerseBaseGame> createState() => _TapVerseBaseGameState();

  /// Override this method to implement game-specific startup logic
  /// Called when the game starts fresh or restarts
  void startGame(WidgetRef ref);

  /// Override this method to implement game-specific reset logic
  /// Should completely reset all game state and remove old entities
  void resetGame(WidgetRef ref);

  /// Override this method to handle game over logic
  /// Called when the game ends but before showing overlays
  void onGameOver(WidgetRef ref) {}

  /// Override this method to clean up game-specific resources
  /// Called when the game is disposed or exited
  void disposeGame(WidgetRef ref) {}

  /// Override this to provide the game content with border walls
  /// The walls will be automatically added around your content
  Widget buildGameContentWithWalls(BuildContext context, WidgetRef ref, GameState gameState);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return Stack(
      children: [
        // Game content
        buildGameContentWithWalls(context, ref, gameState),
        // Border walls overlay
        _buildBorderWalls(context),
      ],
    );
  }

  /// Builds the consistent border walls for all TapVerse games
  Widget _buildBorderWalls(BuildContext context) {
    return CustomPaint(
      painter: _BorderWallsPainter(),
      size: Size.infinite,
    );
  }
}

class _TapVerseBaseGameState extends ConsumerState<TapVerseBaseGame>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeController.forward();

    // Listen for game state changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen(gameStateProvider(widget.config), (previous, next) {
        if (previous?.status != next.status) {
          switch (next.status) {
            case GameStatus.playing:
              if (previous?.status == GameStatus.notStarted) {
                widget.startGame(ref);
              }
              break;
            case GameStatus.gameOver:
              widget.onGameOver(ref);
              _showGameOverDialog(next);
              break;
            default:
              break;
          }
        }
      });
    });
  }

  @override
  void dispose() {
    widget.disposeGame(ref);
    _fadeController.dispose();
    super.dispose();
  }

  void _startGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.startGame();
    widget.onGameStart(ref);
  }

  void _pauseGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.pauseGame();
    widget.onGamePause(ref);
  }

  void _resumeGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.resumeGame();
    widget.onGameResume(ref);
  }

  void _showGameOverDialog(GameState gameState) async {
    final dialog = await GameOverDialog.create(
      ref: ref,
      config: widget.config,
      score: gameState.score,
      onPlayAgain: () {
        // Reset game state and restart
        final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
        gameNotifier.resetGame();
        widget.resetGame(ref);
        _startGame();
      },
      onHome: () {
        widget.onGameExit?.call();
      },
      onLeaderboard: () {
        widget.onGameComplete?.call();
      },
    );

    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => dialog,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(widget.config));

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeController,
          child: Stack(
            children: [
              // Game content with walls
              Positioned.fill(
                child: widget.buildGameContent(context, ref, gameState),
              ),

              // Game UI overlay (score, pause button, etc.)
              Positioned.fill(
                child: GameUIOverlay(
                  config: widget.config,
                  onPause: _pauseGame,
                  onResume: _resumeGame,
                  onStart: _startGame,
                  onExit: widget.onGameExit,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Custom painter for drawing consistent border walls
class _BorderWallsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final wallPaint = Paint()
      ..color = TapVerseGameConstants.wallColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = TapVerseGameConstants.wallBorderColor
      ..strokeWidth = TapVerseGameConstants.wallBorderWidth
      ..style = PaintingStyle.stroke;

    final thickness = TapVerseGameConstants.wallThickness;

    // Draw walls
    // Left wall
    canvas.drawRect(
      Rect.fromLTWH(0, 0, thickness, size.height),
      wallPaint,
    );

    // Right wall
    canvas.drawRect(
      Rect.fromLTWH(size.width - thickness, 0, thickness, size.height),
      wallPaint,
    );

    // Top wall
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, thickness),
      wallPaint,
    );

    // Bottom wall
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - thickness, size.width, thickness),
      wallPaint,
    );

    // Draw borders for better visibility
    // Left wall border
    canvas.drawRect(
      Rect.fromLTWH(thickness - 1, 0, 1, size.height),
      borderPaint,
    );

    // Right wall border
    canvas.drawRect(
      Rect.fromLTWH(size.width - thickness, 0, 1, size.height),
      borderPaint,
    );

    // Top wall border
    canvas.drawRect(
      Rect.fromLTWH(0, thickness - 1, size.width, 1),
      borderPaint,
    );

    // Bottom wall border
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - thickness, size.width, 1),
      borderPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Utility class for handling wall collision detection in TapVerse games
class TapVerseWallCollision {
  static const double wallThickness = TapVerseGameConstants.wallThickness;

  /// Check and handle collision with walls for a circular object (like a ball)
  /// Returns the new position and velocity after collision
  static CollisionResult checkCircularCollision({
    required Offset position,
    required Offset velocity,
    required double radius,
    required Size gameArea,
  }) {
    Offset newPosition = position;
    Offset newVelocity = velocity;
    bool collided = false;

    // Left wall collision
    if (position.dx <= wallThickness + radius) {
      newPosition = Offset(wallThickness + radius, position.dy);
      newVelocity = Offset(-velocity.dx, velocity.dy);
      collided = true;
    }

    // Right wall collision
    else if (position.dx >= gameArea.width - wallThickness - radius) {
      newPosition = Offset(gameArea.width - wallThickness - radius, position.dy);
      newVelocity = Offset(-velocity.dx, velocity.dy);
      collided = true;
    }

    // Top wall collision
    if (position.dy <= wallThickness + radius) {
      newPosition = Offset(newPosition.dx, wallThickness + radius);
      newVelocity = Offset(newVelocity.dx, -newVelocity.dy);
      collided = true;
    }

    // Bottom wall collision
    else if (position.dy >= gameArea.height - wallThickness - radius) {
      newPosition = Offset(newPosition.dx, gameArea.height - wallThickness - radius);
      newVelocity = Offset(newVelocity.dx, -newVelocity.dy);
      collided = true;
    }

    return CollisionResult(
      position: newPosition,
      velocity: newVelocity,
      collided: collided,
    );
  }

  /// Check and handle collision with walls for a rectangular object (like a paddle)
  /// Returns the constrained position within the play area
  static Offset constrainRectangularObject({
    required Offset position,
    required Size objectSize,
    required Size gameArea,
  }) {
    final constrainedX = position.dx.clamp(
      wallThickness,
      gameArea.width - wallThickness - objectSize.width,
    );

    final constrainedY = position.dy.clamp(
      wallThickness,
      gameArea.height - wallThickness - objectSize.height,
    );

    return Offset(constrainedX, constrainedY);
  }

  /// Get the playable area size (excluding walls)
  static Size getPlayableArea(Size gameArea) {
    return Size(
      gameArea.width - (wallThickness * 2),
      gameArea.height - (wallThickness * 2),
    );
  }

  /// Get the playable area offset (top-left corner of play area)
  static Offset getPlayableAreaOffset() {
    return const Offset(wallThickness, wallThickness);
  }
}

/// Result of a collision check
class CollisionResult {
  final Offset position;
  final Offset velocity;
  final bool collided;

  const CollisionResult({
    required this.position,
    required this.velocity,
    required this.collided,
  });
}
