import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import '../widgets/game_grid.dart';
import '../widgets/token_display.dart';
import '../widgets/games_popup_dialog.dart';
import '../../auth/widgets/dismissible_upgrade_popup.dart';
import '../../casino/widgets/floating_casino_button.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _initializeServices();
    _scheduleUpgradePopup();
  }

  void _scheduleUpgradePopup() {
    // Show upgrade popup for anonymous users after a short delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          final accountUpgradeService = ref.read(accountUpgradeServiceProvider);
          if (accountUpgradeService.isAnonymousUser) {
            accountUpgradeService.requestShowUpgradePopup();
          }
        }
      });
    });
  }

  Future<void> _initializeServices() async {
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);
    
    await soundService.initialize();
    await vibrationService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);
    final userTokens = ref.watch(userTokensProvider);
    final accountUpgradeService = ref.watch(accountUpgradeServiceProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'TapVerse',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => gameNavigation.navigateToSettings(context),
          tooltip: 'Settings',
        ),
        actions: [
          // Profile button
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () => gameNavigation.navigateToProfile(context),
            tooltip: 'Profile',
          ),
          // Store button
          IconButton(
            icon: const Icon(Icons.store),
            onPressed: () => gameNavigation.navigateToStore(context),
            tooltip: 'Store',
          ),
          // Token display
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: userTokens.when(
              data: (tokens) => TokenDisplay(tokens: tokens),
              loading: () => const TokenDisplay(tokens: 0),
              error: (_, __) => const TokenDisplay(tokens: 0),
            ),
          ),
        ],
      ),
      body: authState.when(
        data: (user) {
          if (user == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          return Stack(
            children: [
              Column(
                children: [
                  // Welcome section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.primaryContainer,
                          Theme.of(context).colorScheme.secondaryContainer,
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back, ${user.displayName ?? 'Player'}!',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Choose a game to start earning tokens',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Quick stats
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Games',
                            '${gameNavigation.allGames.length}',
                            Icons.games,
                            Colors.blue,
                            onTap: () => _showGamesPopup(context),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Leaderboard',
                            'View',
                            Icons.leaderboard,
                            Colors.green,
                            onTap: () => gameNavigation.navigateToLeaderboard(context),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Store',
                            'Shop',
                            Icons.store,
                            Colors.purple,
                            onTap: () => gameNavigation.navigateToStore(context),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Expanded Games grid (takes more space now)
                  const Expanded(
                    child: GameGrid(),
                  ),
                ],
              ),

              // Floating casino button
              const FloatingCasinoButton(),

              // Dismissible upgrade popup for anonymous users
              if (accountUpgradeService.showUpgradePopup)
                const DismissibleUpgradePopup(),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Something went wrong',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(authStateProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                value,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showGamesPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const GamesPopupDialog(),
    );
  }


}
